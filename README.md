# 知识库系统

一个基于FastAPI和MySQL的云端知识库系统，支持文章管理、分类、标签、搜索等功能。

## 功能特性

- 🔐 用户认证系统（注册、登录、JWT认证）
- 📝 文章管理（创建、编辑、删除、发布）
- 📂 分类管理（支持层级分类）
- 🏷️ 标签系统
- 🔍 全文搜索
- 📱 响应式设计
- 🐳 Docker容器化部署

## 技术栈

- **后端**: Python + FastAPI + SQLAlchemy
- **数据库**: MySQL 8.0
- **前端**: HTML + CSS + JavaScript
- **部署**: Docker + Docker Compose + Nginx

## 快速开始

### 本地开发

1. **克隆项目**
```bash
git clone <repository-url>
cd knowledge-base
```

2. **创建虚拟环境**
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate  # Windows
```

3. **安装依赖**
```bash
pip install -r requirements.txt
```

4. **配置环境变量**
```bash
cp .env.example .env
# 编辑 .env 文件，配置数据库连接等信息
```

5. **启动应用**
```bash
python main.py
```

访问 http://localhost:8000 查看应用。

### Docker部署

1. **使用Docker Compose启动**
```bash
docker-compose up -d
```

2. **查看服务状态**
```bash
docker-compose ps
```

3. **查看日志**
```bash
docker-compose logs -f knowledge-base
```

## API文档

启动应用后，访问以下地址查看API文档：
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## 主要API端点

### 认证
- `POST /api/auth/register` - 用户注册
- `POST /api/auth/login` - 用户登录

### 文章
- `GET /api/articles` - 获取文章列表
- `POST /api/articles` - 创建文章
- `GET /api/articles/{id}` - 获取文章详情
- `PUT /api/articles/{id}` - 更新文章
- `DELETE /api/articles/{id}` - 删除文章

### 分类
- `GET /api/categories` - 获取分类列表
- `POST /api/categories` - 创建分类
- `PUT /api/categories/{id}` - 更新分类
- `DELETE /api/categories/{id}` - 删除分类

### 标签
- `GET /api/tags` - 获取标签列表
- `POST /api/tags` - 创建标签
- `DELETE /api/tags/{id}` - 删除标签

## 数据库设计

### 主要表结构

- `users` - 用户表
- `articles` - 文章表
- `categories` - 分类表
- `tags` - 标签表
- `article_tags` - 文章标签关联表

## 部署到云服务器

### 1. 准备云服务器
- 安装Docker和Docker Compose
- 开放80和8000端口

### 2. 上传代码
```bash
scp -r . user@your-server:/path/to/knowledge-base
```

### 3. 启动服务
```bash
ssh user@your-server
cd /path/to/knowledge-base
docker-compose up -d
```

### 4. 配置域名（可选）
- 配置DNS解析
- 修改nginx.conf中的server_name
- 配置SSL证书

## 连接本地MySQL

如果要连接本地MySQL数据库，可以：

1. **使用SSH隧道**
```bash
ssh -L 3306:localhost:3306 user@your-local-server -N
```

2. **修改docker-compose.yml**
```yaml
environment:
  - DATABASE_URL=mysql+pymysql://user:<EMAIL>:3306/knowledge_base
```

## 开发指南

### 添加新功能
1. 在`models/`目录下创建数据模型
2. 在`app/schemas.py`中添加Pydantic模式
3. 在`routers/`目录下创建API路由
4. 在`main.py`中注册路由

### 数据库迁移
```bash
# 生成迁移文件
alembic revision --autogenerate -m "描述"

# 执行迁移
alembic upgrade head
```

## 常见问题

### 1. 数据库连接失败
- 检查MySQL服务是否启动
- 确认数据库连接字符串正确
- 检查防火墙设置

### 2. 静态文件无法加载
- 确认static目录存在
- 检查文件权限
- 查看nginx配置

### 3. JWT认证失败
- 检查SECRET_KEY配置
- 确认token格式正确
- 查看token是否过期

## 贡献

欢迎提交Issue和Pull Request！

## 许可证

MIT License
