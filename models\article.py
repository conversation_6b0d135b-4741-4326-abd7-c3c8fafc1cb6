from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, ForeignKey, Enum
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.database import Base
from models.tag import article_tags
import enum

class ArticleStatus(enum.Enum):
    DRAFT = "draft"
    PUBLISHED = "published"
    ARCHIVED = "archived"

class Article(Base):
    __tablename__ = "articles"
    
    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(200), index=True, nullable=False)
    slug = Column(String(200), unique=True, index=True)  # URL友好的标识符
    content = Column(Text, nullable=False)
    summary = Column(Text)  # 文章摘要
    cover_image = Column(String(255))  # 封面图片URL
    status = Column(Enum(ArticleStatus), default=ArticleStatus.DRAFT)
    is_featured = Column(Boolean, default=False)  # 是否为精选文章
    view_count = Column(Integer, default=0)  # 浏览次数
    like_count = Column(Integer, default=0)  # 点赞次数
    
    # 外键
    author_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    category_id = Column(Integer, ForeignKey("categories.id"))
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    published_at = Column(DateTime(timezone=True))
    
    # 关系
    author = relationship("User")
    category = relationship("Category", back_populates="articles")
    tags = relationship("Tag", secondary=article_tags, back_populates="articles")
    
    def __repr__(self):
        return f"<Article(title='{self.title}', status='{self.status}')>"
