version: '3.8'

services:
  # 知识库应用
  knowledge-base:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=mysql+pymysql://knowledge_user:knowledge_pass@mysql:3306/knowledge_base
      - SECRET_KEY=your-super-secret-key-change-this-in-production
      - DEBUG=False
    depends_on:
      - mysql
    volumes:
      - ./static:/app/static
      - ./templates:/app/templates
    restart: unless-stopped

  # MySQL数据库
  mysql:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=root_password
      - MYSQL_DATABASE=knowledge_base
      - MYSQL_USER=knowledge_user
      - MYSQL_PASSWORD=knowledge_pass
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    restart: unless-stopped

  # Nginx反向代理（可选）
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./static:/var/www/static
    depends_on:
      - knowledge-base
    restart: unless-stopped

volumes:
  mysql_data:
