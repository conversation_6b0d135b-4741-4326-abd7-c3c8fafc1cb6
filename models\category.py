from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.database import Base

class Category(Base):
    __tablename__ = "categories"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), unique=True, index=True, nullable=False)
    description = Column(Text)
    color = Column(String(7), default="#007bff")  # 十六进制颜色代码
    icon = Column(String(50))  # 图标类名
    parent_id = Column(Integer, ForeignKey("categories.id"))
    created_by = Column(Integer, ForeignKey("users.id"))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # 关系
    parent = relationship("Category", remote_side=[id])
    children = relationship("Category")
    creator = relationship("User")
    articles = relationship("Article", back_populates="category")
    
    def __repr__(self):
        return f"<Category(name='{self.name}')>"
