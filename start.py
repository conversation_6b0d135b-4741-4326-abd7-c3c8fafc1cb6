#!/usr/bin/env python3
"""
知识库系统启动脚本
"""

import os
import sys
import subprocess
from pathlib import Path

def check_requirements():
    """检查系统要求"""
    print("🔍 检查系统要求...")
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("❌ 需要Python 3.8或更高版本")
        return False
    
    print(f"✅ Python版本: {sys.version}")
    
    # 检查是否在虚拟环境中
    if not hasattr(sys, 'real_prefix') and not (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("⚠️  建议在虚拟环境中运行")
    
    return True

def install_dependencies():
    """安装依赖"""
    print("📦 安装依赖...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ 依赖安装完成")
        return True
    except subprocess.CalledProcessError:
        print("❌ 依赖安装失败")
        return False

def setup_environment():
    """设置环境变量"""
    print("⚙️  设置环境...")
    
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if not env_file.exists() and env_example.exists():
        print("📝 创建环境配置文件...")
        env_file.write_text(env_example.read_text(encoding='utf-8'), encoding='utf-8')
        print("✅ 请编辑 .env 文件配置数据库连接")
    
    return True

def start_application():
    """启动应用"""
    print("🚀 启动知识库系统...")
    
    try:
        # 启动FastAPI应用
        subprocess.run([
            sys.executable, "-m", "uvicorn", 
            "main:app", 
            "--host", "0.0.0.0", 
            "--port", "8000", 
            "--reload"
        ])
    except KeyboardInterrupt:
        print("\n👋 应用已停止")

def main():
    """主函数"""
    print("🌟 欢迎使用知识库系统!")
    print("=" * 50)
    
    # 检查系统要求
    if not check_requirements():
        sys.exit(1)
    
    # 安装依赖
    if not install_dependencies():
        sys.exit(1)
    
    # 设置环境
    if not setup_environment():
        sys.exit(1)
    
    print("\n" + "=" * 50)
    print("🎉 系统准备就绪!")
    print("📖 访问 http://localhost:8000 查看应用")
    print("📚 访问 http://localhost:8000/docs 查看API文档")
    print("=" * 50)
    
    # 启动应用
    start_application()

if __name__ == "__main__":
    main()
