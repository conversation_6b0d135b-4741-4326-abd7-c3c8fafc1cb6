// 知识库前端JavaScript

class KnowledgeBase {
    constructor() {
        this.apiBase = '/api';
        this.token = localStorage.getItem('token');
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadArticles();
        this.loadCategories();
        this.loadTags();
    }

    setupEventListeners() {
        // 登录表单
        const loginForm = document.getElementById('loginForm');
        if (loginForm) {
            loginForm.addEventListener('submit', (e) => this.handleLogin(e));
        }

        // 注册表单
        const registerForm = document.getElementById('registerForm');
        if (registerForm) {
            registerForm.addEventListener('submit', (e) => this.handleRegister(e));
        }

        // 文章表单
        const articleForm = document.getElementById('articleForm');
        if (articleForm) {
            articleForm.addEventListener('submit', (e) => this.handleArticleSubmit(e));
        }

        // 搜索
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => this.handleSearch(e));
        }
    }

    // API请求方法
    async apiRequest(endpoint, options = {}) {
        const url = `${this.apiBase}${endpoint}`;
        const config = {
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            ...options
        };

        if (this.token) {
            config.headers['Authorization'] = `Bearer ${this.token}`;
        }

        try {
            const response = await fetch(url, config);
            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.detail || 'API请求失败');
            }
            
            return data;
        } catch (error) {
            console.error('API请求错误:', error);
            this.showMessage(error.message, 'error');
            throw error;
        }
    }

    // 用户认证
    async handleLogin(e) {
        e.preventDefault();
        const formData = new FormData(e.target);
        
        try {
            const response = await fetch(`${this.apiBase}/auth/login`, {
                method: 'POST',
                body: formData
            });
            
            const data = await response.json();
            
            if (response.ok) {
                this.token = data.access_token;
                localStorage.setItem('token', this.token);
                this.showMessage('登录成功！', 'success');
                window.location.href = '/';
            } else {
                throw new Error(data.detail);
            }
        } catch (error) {
            this.showMessage(error.message, 'error');
        }
    }

    async handleRegister(e) {
        e.preventDefault();
        const formData = new FormData(e.target);
        const userData = Object.fromEntries(formData);
        
        try {
            await this.apiRequest('/auth/register', {
                method: 'POST',
                body: JSON.stringify(userData)
            });
            
            this.showMessage('注册成功！请登录。', 'success');
            window.location.href = '/login';
        } catch (error) {
            // 错误已在apiRequest中处理
        }
    }

    // 文章管理
    async loadArticles(search = '', categoryId = null, tagId = null) {
        try {
            let endpoint = '/articles?';
            const params = new URLSearchParams();
            
            if (search) params.append('search', search);
            if (categoryId) params.append('category_id', categoryId);
            if (tagId) params.append('tag_id', tagId);
            
            endpoint += params.toString();
            
            const articles = await this.apiRequest(endpoint);
            this.renderArticles(articles);
        } catch (error) {
            // 错误已在apiRequest中处理
        }
    }

    renderArticles(articles) {
        const container = document.getElementById('articlesContainer');
        if (!container) return;

        if (articles.length === 0) {
            container.innerHTML = '<p>暂无文章</p>';
            return;
        }

        container.innerHTML = articles.map(article => `
            <div class="article-card">
                <h3><a href="/article/${article.slug}" class="article-title">${article.title}</a></h3>
                <div class="article-meta">
                    作者: ${article.author.full_name || article.author.username} | 
                    发布时间: ${new Date(article.created_at).toLocaleDateString()} |
                    浏览: ${article.view_count}
                </div>
                ${article.summary ? `<p class="article-summary">${article.summary}</p>` : ''}
                ${article.tags.length > 0 ? `
                    <div class="tags">
                        ${article.tags.map(tag => `<span class="tag">${tag.name}</span>`).join('')}
                    </div>
                ` : ''}
            </div>
        `).join('');
    }

    async loadCategories() {
        try {
            const categories = await this.apiRequest('/categories');
            this.renderCategories(categories);
        } catch (error) {
            // 错误已在apiRequest中处理
        }
    }

    renderCategories(categories) {
        const container = document.getElementById('categoriesContainer');
        if (!container) return;

        container.innerHTML = categories.map(category => `
            <div class="category-item">
                <a href="#" onclick="app.filterByCategory(${category.id})">${category.name}</a>
            </div>
        `).join('');
    }

    async loadTags() {
        try {
            const tags = await this.apiRequest('/tags');
            this.renderTags(tags);
        } catch (error) {
            // 错误已在apiRequest中处理
        }
    }

    renderTags(tags) {
        const container = document.getElementById('tagsContainer');
        if (!container) return;

        container.innerHTML = tags.map(tag => `
            <span class="tag" onclick="app.filterByTag(${tag.id})">${tag.name}</span>
        `).join('');
    }

    // 筛选方法
    filterByCategory(categoryId) {
        this.loadArticles('', categoryId);
    }

    filterByTag(tagId) {
        this.loadArticles('', null, tagId);
    }

    handleSearch(e) {
        const searchTerm = e.target.value;
        clearTimeout(this.searchTimeout);
        this.searchTimeout = setTimeout(() => {
            this.loadArticles(searchTerm);
        }, 300);
    }

    // 工具方法
    showMessage(message, type = 'info') {
        // 简单的消息显示，可以用更好的UI库替换
        const messageDiv = document.createElement('div');
        messageDiv.className = `message message-${type}`;
        messageDiv.textContent = message;
        messageDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 1rem;
            border-radius: 4px;
            color: white;
            z-index: 1000;
            background: ${type === 'error' ? '#dc3545' : type === 'success' ? '#28a745' : '#007bff'};
        `;
        
        document.body.appendChild(messageDiv);
        
        setTimeout(() => {
            messageDiv.remove();
        }, 3000);
    }

    logout() {
        localStorage.removeItem('token');
        this.token = null;
        window.location.href = '/login';
    }
}

// 初始化应用
const app = new KnowledgeBase();
