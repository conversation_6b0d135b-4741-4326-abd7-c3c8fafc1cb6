from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from sqlalchemy import or_, desc
from app.database import get_db
from app.auth import get_current_active_user, get_current_admin_user
from app.schemas import Article as ArticleSchema, ArticleCreate, ArticleUpdate, ArticleStatusEnum
from models.article import Article, ArticleStatus
from models.user import User
from models.tag import Tag
import re
from datetime import datetime

router = APIRouter(prefix="/articles", tags=["文章"])

def create_slug(title: str) -> str:
    """从标题创建URL友好的slug"""
    # 简单的slug生成，实际项目中可能需要更复杂的逻辑
    slug = re.sub(r'[^\w\s-]', '', title.lower())
    slug = re.sub(r'[-\s]+', '-', slug)
    return slug.strip('-')

@router.post("/", response_model=ArticleSchema)
async def create_article(
    article: ArticleCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """创建文章"""
    # 生成slug
    slug = create_slug(article.title)
    
    # 检查slug是否已存在
    existing_article = db.query(Article).filter(Article.slug == slug).first()
    if existing_article:
        # 如果存在，添加时间戳
        slug = f"{slug}-{int(datetime.now().timestamp())}"
    
    # 创建文章
    db_article = Article(
        title=article.title,
        slug=slug,
        content=article.content,
        summary=article.summary,
        cover_image=article.cover_image,
        category_id=article.category_id,
        author_id=current_user.id
    )
    
    db.add(db_article)
    db.commit()
    db.refresh(db_article)
    
    # 添加标签
    if article.tag_ids:
        tags = db.query(Tag).filter(Tag.id.in_(article.tag_ids)).all()
        db_article.tags = tags
        db.commit()
        db.refresh(db_article)
    
    return db_article

@router.get("/", response_model=List[ArticleSchema])
async def get_articles(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    status: Optional[ArticleStatusEnum] = None,
    category_id: Optional[int] = None,
    tag_id: Optional[int] = None,
    search: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """获取文章列表"""
    query = db.query(Article)
    
    # 状态筛选
    if status:
        query = query.filter(Article.status == ArticleStatus(status.value))
    else:
        # 默认只显示已发布的文章
        query = query.filter(Article.status == ArticleStatus.PUBLISHED)
    
    # 分类筛选
    if category_id:
        query = query.filter(Article.category_id == category_id)
    
    # 标签筛选
    if tag_id:
        query = query.join(Article.tags).filter(Tag.id == tag_id)
    
    # 搜索
    if search:
        search_term = f"%{search}%"
        query = query.filter(
            or_(
                Article.title.like(search_term),
                Article.content.like(search_term),
                Article.summary.like(search_term)
            )
        )
    
    # 排序和分页
    articles = query.order_by(desc(Article.created_at)).offset(skip).limit(limit).all()
    return articles

@router.get("/{article_id}", response_model=ArticleSchema)
async def get_article(article_id: int, db: Session = Depends(get_db)):
    """获取单篇文章"""
    article = db.query(Article).filter(Article.id == article_id).first()
    if not article:
        raise HTTPException(status_code=404, detail="Article not found")
    
    # 增加浏览次数
    article.view_count += 1
    db.commit()
    
    return article

@router.get("/slug/{slug}", response_model=ArticleSchema)
async def get_article_by_slug(slug: str, db: Session = Depends(get_db)):
    """通过slug获取文章"""
    article = db.query(Article).filter(Article.slug == slug).first()
    if not article:
        raise HTTPException(status_code=404, detail="Article not found")
    
    # 增加浏览次数
    article.view_count += 1
    db.commit()
    
    return article

@router.put("/{article_id}", response_model=ArticleSchema)
async def update_article(
    article_id: int,
    article_update: ArticleUpdate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """更新文章"""
    article = db.query(Article).filter(Article.id == article_id).first()
    if not article:
        raise HTTPException(status_code=404, detail="Article not found")
    
    # 检查权限（只有作者或管理员可以编辑）
    if article.author_id != current_user.id and not current_user.is_admin:
        raise HTTPException(status_code=403, detail="Not enough permissions")
    
    # 更新字段
    update_data = article_update.dict(exclude_unset=True)
    
    # 如果更新了标题，重新生成slug
    if "title" in update_data:
        new_slug = create_slug(update_data["title"])
        existing_article = db.query(Article).filter(
            Article.slug == new_slug, 
            Article.id != article_id
        ).first()
        if existing_article:
            new_slug = f"{new_slug}-{int(datetime.now().timestamp())}"
        update_data["slug"] = new_slug
    
    # 处理标签
    if "tag_ids" in update_data:
        tag_ids = update_data.pop("tag_ids")
        if tag_ids is not None:
            tags = db.query(Tag).filter(Tag.id.in_(tag_ids)).all()
            article.tags = tags
    
    # 更新其他字段
    for field, value in update_data.items():
        setattr(article, field, value)
    
    # 如果状态改为已发布，设置发布时间
    if article_update.status == ArticleStatusEnum.PUBLISHED and not article.published_at:
        article.published_at = datetime.utcnow()
    
    db.commit()
    db.refresh(article)
    
    return article

@router.delete("/{article_id}")
async def delete_article(
    article_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """删除文章"""
    article = db.query(Article).filter(Article.id == article_id).first()
    if not article:
        raise HTTPException(status_code=404, detail="Article not found")
    
    # 检查权限（只有作者或管理员可以删除）
    if article.author_id != current_user.id and not current_user.is_admin:
        raise HTTPException(status_code=403, detail="Not enough permissions")
    
    db.delete(article)
    db.commit()
    
    return {"message": "Article deleted successfully"}
